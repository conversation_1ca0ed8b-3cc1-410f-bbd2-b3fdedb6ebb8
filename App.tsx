/**
 * React Native Anime Content App
 * Main application component with React Navigation
 */

import React from 'react';
import 'react-native-gesture-handler'; // Must be at the top
import { StatusBar } from 'react-native';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import ErrorBoundary from './src/components/ErrorBoundary';
import AppNavigator from './src/navigation/AppNavigator';

const App: React.FC = () => {
  return (
    <SafeAreaProvider>
      <ErrorBoundary>
        <StatusBar barStyle="light-content" backgroundColor="#000" />
        <AppNavigator />
      </ErrorBoundary>
    </SafeAreaProvider>
  );
};

export default App;

