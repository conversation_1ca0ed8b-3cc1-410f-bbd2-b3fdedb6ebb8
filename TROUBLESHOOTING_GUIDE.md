# React Native Blank Screen Troubleshooting Guide

If your React Native app is showing a blank screen after installation, this guide will help you identify and fix the issue.

## Common Causes and Solutions

### 1. Metro Bundler Not Running

**Symptoms**: App installs successfully but shows a blank screen.

**Solution**:
1. Open a new terminal window
2. Navigate to your project directory:
   ```bash
   cd /path/to/your/ReactNativeApp
   ```
3. Start Metro Bundler:
   ```bash
   npm start
   # or
   yarn start
   ```
4. Wait for Metro to start (you'll see "Metro waiting on exp://...")
5. Reload your app:
   - **Android Emulator**: Press `R` twice or `Ctrl+M` → Reload
   - **Physical Device**: Shake device → Reload

### 2. Metro Cache Issues

**Symptoms**: App shows blank screen even with Metro running.

**Solution**:
```bash
# Clear Metro cache
npm start -- --reset-cache
# or
yarn start --reset-cache
```

### 3. JavaScript Bundle Loading Issues

**Symptoms**: Metro is running but app still shows blank screen.

**Solution**:
1. Check if your device/emulator can reach the Metro server
2. For physical devices, ensure both device and computer are on the same network
3. Try enabling "Dev Settings" → "Debug server host & port for device" and set it to your computer's IP:8081

### 4. Component Rendering Issues

**Symptoms**: Metro is running, but specific components don't render.

**Solution**:
Check your component code for:
- Missing return statements
- Incorrect JSX syntax
- Unhandled errors in render methods

### 5. Network Configuration Issues

**Symptoms**: Physical device shows blank screen, emulator works fine.

**Solution**:
1. Enable developer options on your Android device
2. Go to Settings → Developer Options → USB Debugging
3. In React Native app, shake device → Dev Settings → Debug server host & port
4. Enter your computer's IP address followed by :8081 (e.g., *************:8081)

## Step-by-Step Debugging Process

### Step 1: Check Metro Bundler
```bash
# In your project directory
npm start
```
Look for output like:
```
Metro waiting on exp://*************:8081
```

### Step 2: Check Device Connection
```bash
# For Android
adb devices
```
You should see your device listed.

### Step 3: Check App Logs
```bash
# For Android
npx react-native log-android
```
Look for any error messages or warnings.

### Step 4: Reload the App
- **Android**: Press `R` twice or shake device → Reload
- **iOS**: Press `Cmd+R` in simulator

### Step 5: Clear Everything and Restart
```bash
# Stop Metro if running (Ctrl+C)
# Clear Metro cache
npm start -- --reset-cache

# In another terminal
# Uninstall app from device
adb uninstall com.reactnativeapp

# Reinstall
npm run android
```

## Advanced Troubleshooting

### Check React Native Doctor
```bash
npx react-native doctor
```
This will check your development environment for common issues.

### Verify App Entry Point
Check that your `index.js` file correctly imports and registers your app:
```javascript
import {AppRegistry} from 'react-native';
import App from './App';
import {name as appName} from './app.json';

AppRegistry.registerComponent(appName, () => App);
```

### Check App.tsx/App.js
Ensure your main App component returns valid JSX:
```typescript
import React from 'react';
import {View, Text} from 'react-native';

const App = () => {
  return (
    <View style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}>
      <Text>Hello World!</Text>
    </View>
  );
};

export default App;
```

### Network Debugging for Physical Devices

1. **Find your computer's IP address**:
   ```bash
   # On Linux/macOS
   ifconfig | grep inet
   
   # On Windows
   ipconfig
   ```

2. **Configure device to connect to Metro**:
   - Shake device → Dev Settings → Debug server host & port
   - Enter: `YOUR_COMPUTER_IP:8081`

3. **Alternative: Use ADB port forwarding**:
   ```bash
   adb reverse tcp:8081 tcp:8081
   ```

## Quick Fix Checklist

- [ ] Metro Bundler is running (`npm start`)
- [ ] Device is connected (`adb devices`)
- [ ] App is reloaded after Metro starts
- [ ] Metro cache is cleared if needed
- [ ] Network configuration is correct for physical devices
- [ ] No JavaScript errors in Metro console
- [ ] App entry point is correctly configured

## If Nothing Works

1. **Create a minimal test app**:
   ```bash
   npx react-native init TestApp
   cd TestApp
   npm run android
   ```

2. **Compare with working app** to identify differences

3. **Check React Native version compatibility** with your dependencies

4. **Reinstall node_modules**:
   ```bash
   rm -rf node_modules
   npm install
   ```

5. **Check for conflicting packages** or version mismatches

Remember: The most common cause is Metro Bundler not running. Always start there!

