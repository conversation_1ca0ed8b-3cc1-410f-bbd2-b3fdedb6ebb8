# React Native Developer Task - Requirements Analysis

## Task Overview
Based on the provided PDF and Postman collection, this is a React Native application that displays anime content fetched from an API. The app should handle HTML content rendering using react-native-render-html.

## API Endpoints Analysis

### 1. Generate Token
- **URL**: `https://swsut62sse.execute-api.ap-south-1.amazonaws.com/prod/generateToken`
- **Method**: POST
- **Body**: `{"email": "<EMAIL>"}`
- **Response**: `{"token": "jwt_token_here"}`

### 2. Get Content
- **URL**: `https://tzab40im77.execute-api.ap-south-1.amazonaws.com/prod/getContent`
- **Method**: GET
- **Auth**: Bearer token from step 1
- **Response Structure**:
```json
{
  "content": {
    "thumbNailImage": "image_url",
    "mainImage": "image_url", 
    "userName": "string",
    "subTitle": "string",
    "text": "html_content",
    "id": number,
    "logo": "image_url",
    "title": "string"
  }
}
```

## App Requirements

### Core Features
1. **Authentication**: Generate and store API token
2. **Content Display**: Fetch and display anime content
3. **HTML Rendering**: Use react-native-render-html for text content
4. **Image Display**: Show thumbnail, main image, and logo
5. **Error Handling**: Handle API failures gracefully
6. **Loading States**: Show loading indicators

### Technical Requirements
- React Native latest version
- react-native-render-html for HTML content
- No Expo (pure React Native CLI)
- Support for iOS or Android
- Unit tests required
- Clean code with comments
- Proper error handling

### UI Components Needed
1. Header with logo and title
2. User information display
3. Image gallery (thumbnail, main image)
4. HTML content renderer
5. Loading spinner
6. Error message display

## Implementation Plan
1. Set up API service layer
2. Create authentication flow
3. Build content display components
4. Implement HTML rendering
5. Add error handling and loading states
6. Write unit tests
7. Create documentation

