import { TokenResponse, AnimeContent } from '../types';

const API_BASE_URL_AUTH =
  'https://swsut62sse.execute-api.ap-south-1.amazonaws.com/prod';
const API_BASE_URL_CONTENT =
  'https://tzab40im77.execute-api.ap-south-1.amazonaws.com/prod';

// 🔴 token stored in memory
let token: string | null = null;

// 🔴 Error factory instead of class
function createApiError(message: string, status?: number, code?: string) {
  return {
    name: 'ApiError',
    message,
    status,
    code,
  };
}

const ApiService = {
  /**
   * Validate email format
   */
  validateEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  },

  /**
   * Generate token
   */
  async generateToken(email: string): Promise<string> {
    if (!email || !this.validateEmail(email)) {
      throw createApiError('Invalid email format', 400, 'VALIDATION_ERROR');
    }

    try {
      const response = await fetch(`${API_BASE_URL_AUTH}/generateToken`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ email }),
      });

      if (!response.ok) {
        const errorText = await response.text().catch(() => 'Unknown error');
        throw createApiError(
          `Authentication failed: ${errorText}`,
          response.status,
          'AUTH_ERROR',
        );
      }

      const data: TokenResponse = await response.json();

      if (!data.token) {
        throw createApiError(
          'Invalid response: missing token',
          500,
          'INVALID_RESPONSE',
        );
      }

      token = data.token;
      return data.token;
    } catch (error: any) {
      if (error?.name === 'ApiError') throw error;
      if (error instanceof TypeError) {
        throw createApiError(
          'Network error: Please check your internet connection',
          0,
          'NETWORK_ERROR',
        );
      }
      throw createApiError(
        'Failed to generate authentication token',
        500,
        'UNKNOWN_ERROR',
      );
    }
  },

  /**
   * Fetch anime content
   */
  async getContent(): Promise<AnimeContent> {
    if (!token) {
      throw createApiError(
        'No authentication token available. Please login first.',
        401,
        'NO_TOKEN',
      );
    }

    try {
      const response = await fetch(`${API_BASE_URL_CONTENT}/getContent`, {
        method: 'GET',
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        if (response.status === 401) {
          this.clearToken();
          throw createApiError(
            'Authentication expired. Please login again.',
            401,
            'TOKEN_EXPIRED',
          );
        }
        const errorText = await response.text().catch(() => 'Unknown error');
        throw createApiError(
          `Failed to fetch content: ${errorText}`,
          response.status,
          'CONTENT_ERROR',
        );
      }

      const data: { content: AnimeContent } = await response.json();
      if (!data.content) {
        throw createApiError(
          'Invalid response: missing content',
          500,
          'INVALID_RESPONSE',
        );
      }

      this.validateContentStructure(data.content);
      return data.content;
    } catch (error: any) {
      if (error?.name === 'ApiError') throw error;
      if (error instanceof TypeError) {
        throw createApiError(
          'Network error: Please check your internet connection',
          0,
          'NETWORK_ERROR',
        );
      }
      throw createApiError('Failed to fetch content', 500, 'UNKNOWN_ERROR');
    }
  },

  /**
   * Validate structure
   */
  validateContentStructure(content: any): void {
    const requiredFields = ['id', 'title', 'text', 'userName'];
    const missingFields = requiredFields.filter(f => !content[f]);
    if (missingFields.length > 0) {
      throw createApiError(
        `Invalid content structure: missing ${missingFields.join(', ')}`,
        500,
        'INVALID_CONTENT',
      );
    }
  },

  isAuthenticated(): boolean {
    return !!token;
  },

  getToken(): string | null {
    return token;
  },

  setToken(newToken: string): void {
    if (!newToken) {
      throw createApiError('Invalid token provided', 400, 'VALIDATION_ERROR');
    }
    token = newToken;
  },

  clearToken(): void {
    token = null;
  },

  async testConnection(): Promise<boolean> {
    try {
      const response = await fetch(`${API_BASE_URL_AUTH}/health`, {
        method: 'GET',
      });
      return response.ok;
    } catch {
      return false;
    }
  },
};

export default ApiService;
