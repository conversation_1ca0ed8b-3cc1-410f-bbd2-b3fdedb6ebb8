/**
 * AsyncStorage Utility
 * Handles persistent storage for user authentication and app data
 */

import AsyncStorage from '@react-native-async-storage/async-storage';

export class AppStorage {
  // Authentication keys
  private static readonly AUTH_TOKEN_KEY = 'auth_token';
  private static readonly USER_EMAIL_KEY = 'user_email';
  private static readonly IS_LOGGED_IN_KEY = 'is_logged_in';
  
  // App data keys
  private static readonly LAST_CONTENT_KEY = 'last_content';
  private static readonly APP_SETTINGS_KEY = 'app_settings';

  /**
   * Store authentication data
   */
  static async setAuthData(token: string, email: string): Promise<void> {
    try {
      await AsyncStorage.setItem(this.AUTH_TOKEN_KEY, token);
      await AsyncStorage.setItem(this.USER_EMAIL_KEY, email);
      await AsyncStorage.setItem(this.IS_LOGGED_IN_KEY, 'true');
    } catch (error) {
      console.error('Error storing auth data:', error);
    }
  }

  /**
   * Get authentication token
   */
  static async getAuthToken(): Promise<string | null> {
    try {
      return await AsyncStorage.getItem(this.AUTH_TOKEN_KEY);
    } catch (error) {
      console.error('Error getting auth token:', error);
      return null;
    }
  }

  /**
   * Get user email
   */
  static async getUserEmail(): Promise<string | null> {
    try {
      return await AsyncStorage.getItem(this.USER_EMAIL_KEY);
    } catch (error) {
      console.error('Error getting user email:', error);
      return null;
    }
  }

  /**
   * Check if user is logged in
   */
  static async isLoggedIn(): Promise<boolean> {
    try {
      const loggedIn = await AsyncStorage.getItem(this.IS_LOGGED_IN_KEY);
      return loggedIn === 'true';
    } catch (error) {
      console.error('Error checking login status:', error);
      return false;
    }
  }

  /**
   * Clear authentication data (logout)
   */
  static async clearAuthData(): Promise<void> {
    try {
      await AsyncStorage.removeItem(this.AUTH_TOKEN_KEY);
      await AsyncStorage.removeItem(this.USER_EMAIL_KEY);
      await AsyncStorage.removeItem(this.IS_LOGGED_IN_KEY);
    } catch (error) {
      console.error('Error clearing auth data:', error);
    }
  }

  /**
   * Store last fetched content for offline viewing
   */
  static async setLastContent(content: any): Promise<void> {
    try {
      await AsyncStorage.setItem(this.LAST_CONTENT_KEY, JSON.stringify(content));
    } catch (error) {
      console.error('Error storing last content:', error);
    }
  }

  /**
   * Get last fetched content
   */
  static async getLastContent(): Promise<any | null> {
    try {
      const contentString = await AsyncStorage.getItem(this.LAST_CONTENT_KEY);
      return contentString ? JSON.parse(contentString) : null;
    } catch (error) {
      console.error('Error getting last content:', error);
      return null;
    }
  }

  /**
   * Store app settings
   */
  static async setAppSettings(settings: any): Promise<void> {
    try {
      await AsyncStorage.setItem(this.APP_SETTINGS_KEY, JSON.stringify(settings));
    } catch (error) {
      console.error('Error storing app settings:', error);
    }
  }

  /**
   * Get app settings
   */
  static async getAppSettings(): Promise<any | null> {
    try {
      const settingsString = await AsyncStorage.getItem(this.APP_SETTINGS_KEY);
      return settingsString ? JSON.parse(settingsString) : null;
    } catch (error) {
      console.error('Error getting app settings:', error);
      return null;
    }
  }

  /**
   * Clear all stored data
   */
  static async clearAll(): Promise<void> {
    try {
      await AsyncStorage.clear();
    } catch (error) {
      console.error('Error clearing all data:', error);
    }
  }

  /**
   * Get all keys stored in AsyncStorage
   */
  static async getAllKeys(): Promise<string[]> {
    try {
      return await AsyncStorage.getAllKeys();
    } catch (error) {
      console.error('Error getting all keys:', error);
      return [];
    }
  }
}

export default AppStorage;

