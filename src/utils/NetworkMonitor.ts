import { AnimeContent } from '../types';

const API_BASE_URL_CONTENT =
  'https://tzab40im77.execute-api.ap-south-1.amazonaws.com/prod';

// Custom ApiError class if not already defined
class ApiError extends Error {
  statusCode: number;
  code: string;

  constructor(message: string, statusCode: number, code: string) {
    super(message);
    this.statusCode = statusCode;
    this.code = code;
  }
}

export async function getContent(): Promise<AnimeContent> {
  try {
    const response = await fetch(`${API_BASE_URL_CONTENT}/getContent`, {
      method: 'GET',
      headers: {
        Authorization: `Bearer eyJhbGciOiJIUzI1NiJ9.aWFtc3dhcm5rYXI3QGdtYWlsLmNvbQ.sfw5gne1v64hpwF3E1S6hJU16kJ48LWN0yJH2Ju6UGc`,
        'Content-Type': 'application/json',
      },
    });

    const data: { content: AnimeContent } = await response.json();

    if (!data.content) {
      throw new ApiError(
        'Invalid response: missing content',
        500,
        'INVALID_RESPONSE',
      );
    }

    return data.content;
  } catch (error: any) {
    if (error instanceof ApiError) {
      throw error;
    }

    if (error instanceof TypeError) {
      throw new ApiError(
        'Network error: Please check your internet connection',
        0,
        'NETWORK_ERROR',
      );
    }

    throw new ApiError('Failed to fetch content', 500, 'UNKNOWN_ERROR');
  }
}
