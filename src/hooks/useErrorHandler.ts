/**
 * useErrorHandler Hook
 * Custom hook for handling errors with retry functionality
 */

import { useState, useCallback } from 'react';
import Error<PERSON>and<PERSON>, { AppError } from '../utils/ErrorHandler';

interface UseErrorHandlerReturn {
  error: AppError | null;
  setError: (error: unknown, context?: string) => void;
  clearError: () => void;
  retry: (fn: () => Promise<void>, maxAttempts?: number) => Promise<void>;
  isRetrying: boolean;
}

export const useErrorHandler = (): UseErrorHandlerReturn => {
  const [error, setErrorState] = useState<AppError | null>(null);
  const [isRetrying, setIsRetrying] = useState(false);

  const setError = useCallback((error: unknown, context?: string) => {
    const appError = ErrorHandler.handleError(error, context);
    setErrorState(appError);
  }, []);

  const clearError = useCallback(() => {
    setErrorState(null);
  }, []);

  const retry = useCallback(async (
    fn: () => Promise<void>,
    maxAttempts: number = 3
  ): Promise<void> => {
    setIsRetrying(true);
    let lastError: unknown;

    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        await fn();
        clearError();
        setIsRetrying(false);
        return;
      } catch (error) {
        lastError = error;
        
        if (attempt < maxAttempts) {
          const appError = ErrorHandler.handleError(error, 'retry');
          if (appError.retryable) {
            const delay = ErrorHandler.getRetryDelay(appError, attempt);
            await new Promise(resolve => setTimeout(resolve, delay));
          } else {
            // If error is not retryable, don't continue retrying
            break;
          }
        }
      }
    }

    // If we get here, all attempts failed
    setError(lastError, 'retry_failed');
    setIsRetrying(false);
  }, [setError, clearError]);

  return {
    error,
    setError,
    clearError,
    retry,
    isRetrying,
  };
};

export default useErrorHandler;

