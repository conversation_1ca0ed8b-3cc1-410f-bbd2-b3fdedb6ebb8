/**
 * ContentScreen Component
 * Displays anime content with HTML rendering
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  Image,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
  Dimensions,
} from 'react-native';
import RenderHtml from 'react-native-render-html';
import LoadingSpinner from './LoadingSpinner';
import ErrorDisplay from './ErrorDisplay';
import { useErrorHandler } from '../hooks/useErrorHandler';
import { AnimeContent } from '../types';
import ApiService from '../services/ApiService';

const { width: screenWidth } = Dimensions.get('window');

interface ContentScreenProps {
  onLogout: () => void;
}

const ContentScreen: React.FC<ContentScreenProps> = ({ onLogout }) => {
  const [content, setContent] = useState<AnimeContent | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const { error, setError, clearError, retry, isRetrying } = useErrorHandler();

  const fetchContent = async () => {
    try {
      clearError();
      const data = await ApiService.getContent();
      setContent(data);
    } catch (err) {
      setError(err, 'fetch_content');
    } finally {
      setIsLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    fetchContent();
  }, []);

  const handleRefresh = () => {
    setRefreshing(true);
    fetchContent();
  };

  const handleRetry = () => {
    setIsLoading(true);
    retry(fetchContent);
  };

  const handleLogout = () => {
    ApiService.clearToken();
    onLogout();
  };

  if (isLoading && !content) {
    return <LoadingSpinner text="Loading content..." />;
  }

  if (error && !content) {
    return (
      <View style={styles.container}>
        <ErrorDisplay 
          error={error}
          onRetry={handleRetry}
          style={styles.fullScreenError}
        />
        <TouchableOpacity style={styles.logoutButton} onPress={handleLogout}>
          <Text style={styles.logoutButtonText}>Logout</Text>
        </TouchableOpacity>
      </View>
    );
  }

  if (!content) {
    return (
      <View style={styles.container}>
        <Text style={styles.noContentText}>No content available</Text>
        <TouchableOpacity style={styles.logoutButton} onPress={handleLogout}>
          <Text style={styles.logoutButtonText}>Logout</Text>
        </TouchableOpacity>
      </View>
    );
  }

  const htmlContent = {
    html: content.text || '<p>No content available</p>',
  };

  return (
    <View style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
        }
      >
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.headerContent}>
            {content.logo && (
              <Image source={{ uri: content.logo }} style={styles.logo} />
            )}
            <View style={styles.headerText}>
              <Text style={styles.title}>{content.title}</Text>
              <Text style={styles.subtitle}>{content.subTitle}</Text>
            </View>
          </View>
          <TouchableOpacity style={styles.logoutButton} onPress={handleLogout}>
            <Text style={styles.logoutButtonText}>Logout</Text>
          </TouchableOpacity>
        </View>

        {/* User Info */}
        <View style={styles.userInfo}>
          <Text style={styles.userName}>By {content.userName}</Text>
          <Text style={styles.contentId}>ID: {content.id}</Text>
        </View>

        {/* Thumbnail Image */}
        {content.thumbNailImage && (
          <View style={styles.imageContainer}>
            <Image 
              source={{ uri: content.thumbNailImage }} 
              style={styles.thumbnailImage}
              resizeMode="cover"
            />
          </View>
        )}

        {/* Main Image */}
        {content.mainImage && (
          <View style={styles.imageContainer}>
            <Image 
              source={{ uri: content.mainImage }} 
              style={styles.mainImage}
              resizeMode="cover"
            />
          </View>
        )}

        {/* HTML Content */}
        <View style={styles.contentContainer}>
          <RenderHtml
            contentWidth={screenWidth - 32}
            source={htmlContent}
            tagsStyles={{
              p: styles.paragraph,
              h1: styles.heading1,
              h2: styles.heading2,
              h3: styles.heading3,
            }}
          />
        </View>

        {/* Error Display for refresh errors */}
        {error && content && (
          <ErrorDisplay 
            error={error}
            onRetry={handleRetry}
            style={styles.refreshError}
          />
        )}
      </ScrollView>

      {/* Loading overlay for retries */}
      {isRetrying && (
        <View style={styles.loadingOverlay}>
          <LoadingSpinner text="Retrying..." />
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  scrollView: {
    flex: 1,
  },
  header: {
    backgroundColor: '#fff',
    padding: 16,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  logo: {
    width: 48,
    height: 48,
    borderRadius: 24,
    marginRight: 12,
  },
  headerText: {
    flex: 1,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 14,
    color: '#666',
  },
  logoutButton: {
    backgroundColor: '#ff4444',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 6,
  },
  logoutButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
  },
  userInfo: {
    backgroundColor: '#fff',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  userName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  contentId: {
    fontSize: 12,
    color: '#888',
  },
  imageContainer: {
    backgroundColor: '#fff',
    padding: 16,
    marginBottom: 8,
  },
  thumbnailImage: {
    width: '100%',
    height: 200,
    borderRadius: 8,
  },
  mainImage: {
    width: '100%',
    height: 300,
    borderRadius: 8,
  },
  contentContainer: {
    backgroundColor: '#fff',
    padding: 16,
    marginBottom: 16,
  },
  paragraph: {
    fontSize: 16,
    lineHeight: 24,
    color: '#333',
    marginBottom: 12,
  },
  heading1: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 16,
  },
  heading2: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 12,
  },
  heading3: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },
  fullScreenError: {
    margin: 16,
  },
  refreshError: {
    margin: 16,
  },
  noContentText: {
    fontSize: 18,
    color: '#666',
    textAlign: 'center',
    marginTop: 100,
  },
  loadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default ContentScreen;

