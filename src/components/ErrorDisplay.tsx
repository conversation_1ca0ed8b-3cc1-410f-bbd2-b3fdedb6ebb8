/**
 * ErrorDisplay Component
 * Displays error messages with retry functionality
 */

import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ViewStyle,
} from 'react-native';
import { AppError, ErrorType } from '../utils/ErrorHandler';

interface ErrorDisplayProps {
  error: AppError | string | null;
  onRetry?: () => void;
  style?: ViewStyle;
  showRetry?: boolean;
}

const ErrorDisplay: React.FC<ErrorDisplayProps> = ({
  error,
  onRetry,
  style,
  showRetry = true,
}) => {
  if (!error) return null;

  const errorMessage = typeof error === 'string' ? error : error.message;
  const isRetryable = typeof error === 'object' && error.retryable;
  const errorType = typeof error === 'object' ? error.type : ErrorType.UNKNOWN;

  const getErrorIcon = (type: ErrorType): string => {
    switch (type) {
      case ErrorType.NETWORK:
        return '📡';
      case ErrorType.AUTHENTICATION:
        return '🔐';
      case ErrorType.VALIDATION:
        return '⚠️';
      case ErrorType.SERVER:
        return '🔧';
      case ErrorType.TIMEOUT:
        return '⏱️';
      case ErrorType.PERMISSION:
        return '🚫';
      default:
        return '❌';
    }
  };

  const getErrorColor = (type: ErrorType): string => {
    switch (type) {
      case ErrorType.NETWORK:
        return '#FF9500';
      case ErrorType.AUTHENTICATION:
        return '#FF3B30';
      case ErrorType.VALIDATION:
        return '#FF9500';
      case ErrorType.SERVER:
        return '#FF3B30';
      case ErrorType.TIMEOUT:
        return '#FF9500';
      case ErrorType.PERMISSION:
        return '#FF3B30';
      default:
        return '#FF3B30';
    }
  };

  return (
    <View style={[styles.container, style]}>
      <View style={styles.content}>
        <Text style={styles.icon}>{getErrorIcon(errorType)}</Text>
        <Text style={[styles.message, { color: getErrorColor(errorType) }]}>
          {errorMessage}
        </Text>
        
        {showRetry && isRetryable && onRetry && (
          <TouchableOpacity style={styles.retryButton} onPress={onRetry}>
            <Text style={styles.retryButtonText}>Retry</Text>
          </TouchableOpacity>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 16,
    backgroundColor: '#fff',
    borderRadius: 8,
    margin: 16,
    borderLeftWidth: 4,
    borderLeftColor: '#FF3B30',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  content: {
    alignItems: 'center',
  },
  icon: {
    fontSize: 32,
    marginBottom: 8,
  },
  message: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 16,
    lineHeight: 22,
  },
  retryButton: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 24,
    paddingVertical: 10,
    borderRadius: 6,
  },
  retryButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
  },
});

export default ErrorDisplay;

