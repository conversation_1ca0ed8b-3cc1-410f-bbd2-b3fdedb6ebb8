import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import LoginScreen from '../screens/LoginScreen';
import ContentScreen from '../screens/ContentScreen';
import FullContentScreen from '../screens/FullContentScreen';
import { AnimeContent } from '../types';
import HomeScreen from '../screens/HomeScreen';
import ContentDetailScreen from '../screens/ContentDetailScreen';

export type RootStackParamList = {
  Login: undefined;
  Content: undefined;
  FullContent: { FullContent: AnimeContent };
  Home: undefined;
  ContentDetail: { ContentDetail: AnimeContent };
};

const Stack = createStackNavigator<RootStackParamList>();

const AppNavigator: React.FC = () => {
  return (
    <NavigationContainer>
      <Stack.Navigator
        initialRouteName="Login"
        screenOptions={{
          headerStyle: {
            backgroundColor: '#007AFF',
          },
          headerTintColor: '#fff',
          headerTitleStyle: {
            fontWeight: 'bold',
          },
        }}
      >
        <Stack.Screen
          name="Login"
          component={LoginScreen}
          options={{
            headerShown: false,
          }}
        />
        <Stack.Screen
          name="Content"
          component={ContentScreen}
          options={{
            headerShown: false,
          }}
        />
        <Stack.Screen
          name="FullContent"
          component={FullContentScreen}
          options={{
            headerShown: false,
          }}
        />
        <Stack.Screen
          name="Home"
          component={HomeScreen}
          options={{
            headerShown: false,
          }}
        />
        <Stack.Screen
          name="ContentDetail"
          component={ContentDetailScreen}
          options={{
            headerShown: false,
          }}
        />
      </Stack.Navigator>
    </NavigationContainer>
  );
};

export default AppNavigator;
