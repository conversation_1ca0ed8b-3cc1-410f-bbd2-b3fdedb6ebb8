import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  Image,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Dimensions,
  Alert,
} from 'react-native';
import { StackNavigationProp } from '@react-navigation/stack';
import LoadingSpinner from '../components/LoadingSpinner';
import ErrorDisplay from '../components/ErrorDisplay';
import { useErrorHandler } from '../hooks/useErrorHandler';
import { AnimeContent } from '../types';
import ApiService from '../services/ApiService';
import AppStorage from '../storage/AsyncStorage';
import { RootStackParamList } from '../navigation/AppNavigator';

type HomeScreenNavigationProp = StackNavigationProp<RootStackParamList, 'Home'>;

interface HomeScreenProps {
  navigation: HomeScreenNavigationProp;
}

const HomeScreen: React.FC<HomeScreenProps> = ({ navigation }) => {
  const [content, setContent] = useState<AnimeContent | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const { error, setError, clearError, retry, isRetrying } = useErrorHandler();

  useEffect(() => {
    loadContent();
  }, []);

  const loadContent = async () => {
    try {
      clearError();

      // Try to load from cache first
      const cachedContent = await AppStorage.getLastContent();
      if (cachedContent && !content) {
        setContent(cachedContent);
      }

      // Fetch fresh content
      const data = await ApiService.getContent();
      setContent(data);

      // Store in cache
      await AppStorage.setLastContent(data);
    } catch (err) {
      setError(err, 'fetch_content');

      // If we have cached content, show it
      const cachedContent = await AppStorage.getLastContent();
      if (cachedContent && !content) {
        setContent(cachedContent);
      }
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  };

  const handleRefresh = () => {
    setIsRefreshing(true);
    loadContent();
  };

  const handleRetry = () => {
    setIsLoading(true);
    retry(loadContent);
  };

  const handleLogout = () => {
    Alert.alert('Logout', 'Are you sure you want to logout?', [
      { text: 'Cancel', style: 'cancel' },
      {
        text: 'Logout',
        style: 'destructive',
        onPress: async () => {
          ApiService.clearToken();
          navigation.replace('Login');
        },
      },
    ]);
  };

  const handleContentPress = () => {
    if (content) {
      navigation.navigate('ContentDetail', { content });
    }
  };

  const getCurrentDate = () => {
    const now = new Date();
    const options: Intl.DateTimeFormatOptions = {
      weekday: 'long',
      day: 'numeric',
      month: 'long',
    };
    return now.toLocaleDateString('en-US', options).toUpperCase();
  };

  if (isLoading && !content) {
    return <LoadingSpinner text="Loading content..." />;
  }

  if (error && !content) {
    return (
      <View style={styles.container}>
        <View style={styles.header}>
          {/* <Text style={styles.dateText}>{getCurrentDate()}</Text> */}
          {/* <Text style={styles.titleText}>Today</Text> */}
          <TouchableOpacity style={styles.logoutButton} onPress={handleLogout}>
            <Text style={styles.logoutButtonText}>Logout</Text>
          </TouchableOpacity>
        </View>
        <ErrorDisplay
          error={error}
          onRetry={handleRetry}
          style={styles.fullScreenError}
        />
      </View>
    );
  }

  if (!content) {
    return (
      <View style={styles.container}>
        <View style={styles.header}>
          {/* <Text style={styles.dateText}>{getCurrentDate()}</Text> */}
          {/* <Text style={styles.titleText}>Today</Text> */}
          <TouchableOpacity style={styles.logoutButton} onPress={handleLogout}>
            <Text style={styles.logoutButtonText}>Logout</Text>
          </TouchableOpacity>
        </View>
        <View style={styles.noContentContainer}>
          <Text style={styles.noContentText}>No content available</Text>
          <TouchableOpacity
            style={styles.refreshButton}
            onPress={handleRefresh}
          >
            <Text style={styles.refreshButtonText}>Refresh</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
      >
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.dateText}>{getCurrentDate()}</Text>
          <View style={styles.titleRow}>
            <Text style={styles.titleText}>Today</Text>
            <Text style={styles.vsText}>vs</Text>
          </View>
          <TouchableOpacity style={styles.logoutButton} onPress={handleLogout}>
            <Text style={styles.logoutButtonText}>Logout</Text>
          </TouchableOpacity>
        </View>

        {/* Main Content Card */}
        <TouchableOpacity
          style={styles.contentCard}
          onPress={handleContentPress}
        >
          {/* Main Image */}
          {content.mainImage && (
            <Image
              source={{ uri: content.mainImage }}
              style={styles.mainImage}
              resizeMode="cover"
            />
          )}

          {/* Content Info */}
          <View style={styles.contentInfo}>
            <View style={styles.animeInfo}>
              {content.logo && (
                <Image
                  source={{ uri: content.logo }}
                  style={styles.logo}
                  resizeMode="cover"
                />
              )}
              <View style={styles.textInfo}>
                <Text style={styles.animeTitle}>{content.title}</Text>
                <Text style={styles.animeSubtitle}>{content.subTitle}</Text>
              </View>
              <TouchableOpacity
                style={styles.refreshButton}
                onPress={handleRefresh}
                disabled={isRefreshing}
              >
                <Text style={styles.refreshButtonText}>
                  {isRefreshing ? 'REFRESHING...' : 'REFRESH'}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </TouchableOpacity>

        {/* Error Display for refresh errors */}
        {error && content && (
          <ErrorDisplay
            error={error}
            onRetry={handleRetry}
            style={styles.refreshError}
          />
        )}

        {/* Bottom spacing */}
        <View style={styles.bottomSpacing} />
      </ScrollView>

      {/* Loading overlay for retries */}
      {isRetrying && (
        <View style={styles.loadingOverlay}>
          <LoadingSpinner text="Retrying..." />
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  scrollView: {
    flex: 1,
  },
  header: {
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 10,
  },
  dateText: {
    fontSize: 12,
    color: '#888',
    marginBottom: 4,
  },
  titleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  titleText: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#fff',
    marginRight: 10,
  },
  vsText: {
    fontSize: 16,
    color: '#888',
  },
  logoutButton: {
    position: 'absolute',
    top: 20,
    right: 20,
    backgroundColor: '#ff4444',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
  },
  logoutButtonText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '600',
  },
  contentCard: {
    backgroundColor: '#fff',
    marginHorizontal: 20,
    borderRadius: 16,
    overflow: 'hidden',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
  },
  mainImage: {
    width: '100%',
    height: 300,
  },
  contentInfo: {
    padding: 16,
  },
  animeInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  logo: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 12,
  },
  textInfo: {
    flex: 1,
  },
  animeTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 2,
  },
  animeSubtitle: {
    fontSize: 12,
    color: '#666',
  },
  refreshButton: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 6,
  },
  refreshButtonText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '600',
  },
  fullScreenError: {
    margin: 16,
  },
  refreshError: {
    margin: 16,
  },
  noContentContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  noContentText: {
    fontSize: 18,
    color: '#666',
    textAlign: 'center',
    marginBottom: 20,
  },
  bottomSpacing: {
    height: 40,
  },
  loadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default HomeScreen;
