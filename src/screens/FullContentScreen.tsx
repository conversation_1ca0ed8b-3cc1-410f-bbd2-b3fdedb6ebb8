import React from 'react';
import {
  View,
  Text,
  Image,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Dimensions,
} from 'react-native';
import { StackNavigationProp } from '@react-navigation/stack';
import { RouteProp } from '@react-navigation/native';
import RenderHtml from 'react-native-render-html';
import { RootStackParamList } from '../navigation/AppNavigator';

const { width: screenWidth } = Dimensions.get('window');

type FullContentScreenNavigationProp = StackNavigationProp<
  RootStackParamList,
  'FullContent'
>;
type FullContentScreenRouteProp = RouteProp<RootStackParamList, 'FullContent'>;

interface FullContentScreenProps {
  navigation: FullContentScreenNavigationProp;
  route: FullContentScreenRouteProp;
}

const FullContentScreen: React.FC<FullContentScreenProps> = ({
  navigation,
  route,
}) => {
  const { content } = route.params;

  const htmlContent = {
    html: content.text || '<p>No content available</p>',
  };

  return (
    <View style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
      >
        {/* Content Header */}
        <View style={styles.contentHeader}>
          <Text style={styles.fullContentTitle}>{content.title}</Text>
          <Text style={styles.fullContentSubtitle}>{content.subTitle}</Text>
        </View>

        {/* Thumbnail Image */}
        {content.thumbNailImage && (
          <View style={styles.imageContainer}>
            <Image
              source={{ uri: content.thumbNailImage }}
              style={styles.thumbnailImage}
              resizeMode="cover"
            />
          </View>
        )}

        {/* Full HTML Content */}
        <View style={styles.htmlContentContainer}>
          <RenderHtml
            contentWidth={screenWidth - 40}
            source={htmlContent}
            tagsStyles={{
              p: styles.paragraph,
              h1: styles.heading1,
              h2: styles.heading2,
              h3: styles.heading3,
              body: styles.bodyText,
            }}
          />
        </View>

        {/* Content Footer */}
        <View style={styles.contentFooter}>
          <View style={styles.animeInfo}>
            {content.logo && (
              <Image
                source={{ uri: content.logo }}
                style={styles.logo}
                resizeMode="cover"
              />
            )}
            <View style={styles.textInfo}>
              <Text style={styles.animeTitle}>{content.title}</Text>
              <Text style={styles.animeSubtitle}>{content.subTitle}</Text>
            </View>
            <TouchableOpacity style={styles.refreshButton}>
              <Text style={styles.refreshButtonText}>REFRESH</Text>
            </TouchableOpacity>
          </View>

          {/* User Info */}
          <View style={styles.userInfo}>
            <Text style={styles.userName}>By {content.userName}</Text>
            <Text style={styles.contentId}>Content ID: {content.id}</Text>
          </View>
        </View>

        {/* Bottom spacing */}
        <View style={styles.bottomSpacing} />
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  scrollView: {
    flex: 1,
  },
  contentHeader: {
    backgroundColor: '#fff',
    paddingHorizontal: 20,
    paddingVertical: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  fullContentTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
    textAlign: 'center',
  },
  fullContentSubtitle: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    lineHeight: 22,
  },
  imageContainer: {
    backgroundColor: '#fff',
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  thumbnailImage: {
    width: '100%',
    height: 250,
    borderRadius: 12,
  },
  htmlContentContainer: {
    backgroundColor: '#fff',
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  paragraph: {
    fontSize: 16,
    lineHeight: 26,
    color: '#333',
    marginBottom: 16,
    textAlign: 'justify',
  },
  heading1: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 16,
    marginTop: 8,
  },
  heading2: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 12,
    marginTop: 8,
  },
  heading3: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
    marginTop: 8,
  },
  bodyText: {
    fontSize: 16,
    lineHeight: 26,
    color: '#333',
  },
  contentFooter: {
    backgroundColor: '#fff',
    paddingHorizontal: 20,
    paddingVertical: 20,
    borderTopWidth: 1,
    borderTopColor: '#eee',
  },
  animeInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  logo: {
    width: 48,
    height: 48,
    borderRadius: 24,
    marginRight: 12,
  },
  textInfo: {
    flex: 1,
  },
  animeTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  animeSubtitle: {
    fontSize: 14,
    color: '#666',
  },
  refreshButton: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
  },
  refreshButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
  },
  userInfo: {
    borderTopWidth: 1,
    borderTopColor: '#eee',
    paddingTop: 16,
  },
  userName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  contentId: {
    fontSize: 12,
    color: '#888',
  },
  bottomSpacing: {
    height: 40,
  },
});

export default FullContentScreen;
