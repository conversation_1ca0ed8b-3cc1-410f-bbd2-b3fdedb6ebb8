
import React from 'react';
import {
  View,
  Text,
  Image,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Dimensions,
} from 'react-native';
import { StackNavigationProp } from '@react-navigation/stack';
import { RouteProp } from '@react-navigation/native';
import RenderHtml from 'react-native-render-html';
import { RootStackParamList } from '../navigation/AppNavigator';

const { width: screenWidth } = Dimensions.get('window');

type ContentDetailScreenNavigationProp = StackNavigationProp<RootStackParamList, 'ContentDetail'>;
type ContentDetailScreenRouteProp = RouteProp<RootStackParamList, 'ContentDetail'>;

interface ContentDetailScreenProps {
  navigation: ContentDetailScreenNavigationProp;
  route: ContentDetailScreenRouteProp;
}

const ContentDetailScreen: React.FC<ContentDetailScreenProps> = ({ navigation, route }) => {
  const { content } = route.params;

  const handleScrollToFullContent = () => {
    navigation.navigate('FullContent', { content });
  };

  // Extract first paragraph or truncate HTML content
  const getPreviewContent = (htmlContent: string) => {
    // Simple extraction of first paragraph or first 200 characters
    const textContent = htmlContent.replace(/<[^>]*>/g, '');
    return textContent.length > 200 ? textContent.substring(0, 200) + '...' : textContent;
  };

  const previewHtml = {
    html: `<p>${getPreviewContent(content.text)}</p>`,
  };

  return (
    <View style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Header with large image */}
        <View style={styles.headerContainer}>
          {content.mainImage && (
            <Image 
              source={{ uri: content.mainImage }} 
              style={styles.headerImage}
              resizeMode="cover"
            />
          )}
          <View style={styles.overlay}>
            <Text style={styles.majorUpdate}>MAJOR UPDATE</Text>
            <Text style={styles.mainTitle}>{content.title}</Text>
          </View>
        </View>

        {/* Content Info */}
        <View style={styles.contentContainer}>
          <View style={styles.animeInfo}>
            {content.logo && (
              <Image 
                source={{ uri: content.logo }} 
                style={styles.logo}
                resizeMode="cover"
              />
            )}
            <View style={styles.textInfo}>
              <Text style={styles.animeTitle}>{content.title}</Text>
              <Text style={styles.animeSubtitle}>{content.subTitle}</Text>
            </View>
            <TouchableOpacity style={styles.refreshButton}>
              <Text style={styles.refreshButtonText}>REFRESH</Text>
            </TouchableOpacity>
          </View>

          {/* Preview Content */}
          <View style={styles.previewContainer}>
            <RenderHtml
              contentWidth={screenWidth - 40}
              source={previewHtml}
              tagsStyles={{
                p: styles.previewText,
              }}
            />
            
            <TouchableOpacity 
              style={styles.readMoreButton} 
              onPress={handleScrollToFullContent}
            >
              <Text style={styles.readMoreText}>Read More...</Text>
            </TouchableOpacity>
          </View>

          {/* User Info */}
          <View style={styles.userInfo}>
            <Text style={styles.userName}>By {content.userName}</Text>
            <Text style={styles.contentId}>ID: {content.id}</Text>
          </View>
        </View>

        {/* Bottom spacing */}
        <View style={styles.bottomSpacing} />
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  scrollView: {
    flex: 1,
  },
  headerContainer: {
    position: 'relative',
    height: 400,
  },
  headerImage: {
    width: '100%',
    height: '100%',
  },
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.4)',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  majorUpdate: {
    fontSize: 12,
    color: '#fff',
    fontWeight: '600',
    marginBottom: 8,
    opacity: 0.8,
  },
  mainTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#fff',
    textAlign: 'center',
    lineHeight: 34,
  },
  contentContainer: {
    backgroundColor: '#fff',
    marginTop: -20,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingTop: 20,
    paddingHorizontal: 20,
    flex: 1,
  },
  animeInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  logo: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 12,
  },
  textInfo: {
    flex: 1,
  },
  animeTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 2,
  },
  animeSubtitle: {
    fontSize: 12,
    color: '#666',
  },
  refreshButton: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 6,
  },
  refreshButtonText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '600',
  },
  previewContainer: {
    marginBottom: 20,
  },
  previewText: {
    fontSize: 16,
    lineHeight: 24,
    color: '#333',
    marginBottom: 16,
  },
  readMoreButton: {
    alignSelf: 'flex-start',
  },
  readMoreText: {
    fontSize: 16,
    color: '#007AFF',
    fontWeight: '600',
  },
  userInfo: {
    borderTopWidth: 1,
    borderTopColor: '#eee',
    paddingTop: 16,
    marginBottom: 20,
  },
  userName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  contentId: {
    fontSize: 12,
    color: '#888',
  },
  bottomSpacing: {
    height: 40,
  },
});

export default ContentDetailScreen;

