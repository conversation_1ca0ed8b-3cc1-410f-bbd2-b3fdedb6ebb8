import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  Image,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
  Alert,
} from 'react-native';
import { StackNavigationProp } from '@react-navigation/stack';
import LoadingSpinner from '../components/LoadingSpinner';
import ErrorDisplay from '../components/ErrorDisplay';
import { useErrorHandler } from '../hooks/useErrorHandler';
import { AnimeContent } from '../types';
import ApiService from '../services/ApiService';
import AppStorage from '../storage/AsyncStorage';
import { RootStackParamList } from '../navigation/AppNavigator';

type ContentScreenNavigationProp = StackNavigationProp<
  RootStackParamList,
  'Content'
>;

interface ContentScreenProps {
  navigation: ContentScreenNavigationProp;
}

const ContentScreen: React.FC<ContentScreenProps> = ({ navigation }) => {
  const [content, setContent] = useState<AnimeContent | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const { error, setError, clearError, retry, isRetrying } = useErrorHandler();

  useEffect(() => {
    loadContent();
  }, []);

  const loadContent = async (forceRefresh = false) => {
    try {
      clearError();

      // Try to load from cache first (only if not force refreshing)
      if (!forceRefresh) {
        const cachedContent = await AppStorage.getLastContent();
        if (cachedContent && !content) {
          setContent(cachedContent);
        }
      }

      // Fetch fresh content from API
      const data = await ApiService.getContent();
      setContent(data);

      // Store in cache
      await AppStorage.setLastContent(data);

      // Increment refresh count to show new content is loaded
    } catch (err) {
      setError(err, 'fetch_content');

      // If we have cached content and this is not a force refresh, show it
      if (!forceRefresh) {
        const cachedContent = await AppStorage.getLastContent();
        if (cachedContent && !content) {
          setContent(cachedContent);
        }
      }
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  };

  const handleRefresh = () => {
    setIsRefreshing(true);
    loadContent(true); // Force refresh to get new content
  };

  const handleRetry = () => {
    setIsLoading(true);
    retry(() => loadContent(true));
  };

  const handleLogout = () => {
    Alert.alert('Logout', 'Are you sure you want to logout?', [
      { text: 'Cancel', style: 'cancel' },
      {
        text: 'Logout',
        style: 'destructive',
        onPress: async () => {
          await AppStorage.clearAuthData();
          ApiService.clearToken();
          navigation.replace('Login');
        },
      },
    ]);
  };

  const getCurrentDate = () => {
    const now = new Date();
    const options: Intl.DateTimeFormatOptions = {
      weekday: 'long',
      day: 'numeric',
      month: 'long',
    };
    return now.toLocaleDateString('en-US', options).toUpperCase();
  };

  if (isLoading && !content) {
    return <LoadingSpinner text="Loading content..." />;
  }

  if (error && !content) {
    return (
      <View style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.titleText}>Today</Text>
          <TouchableOpacity style={styles.logoutButton} onPress={handleLogout}>
            <Text style={styles.logoutButtonText}>Logout</Text>
          </TouchableOpacity>
        </View>
        <ErrorDisplay
          error={error}
          onRetry={handleRetry}
          style={styles.fullScreenError}
        />
      </View>
    );
  }

  if (!content) {
    return (
      <View style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.dateText}>{getCurrentDate()}</Text>
          <Text style={styles.titleText}>Today</Text>
          <TouchableOpacity style={styles.logoutButton} onPress={handleLogout}>
            <Text style={styles.logoutButtonText}>Logout</Text>
          </TouchableOpacity>
        </View>
        <View style={styles.noContentContainer}>
          <Text style={styles.noContentText}>No content available</Text>
          <TouchableOpacity
            style={styles.refreshButton}
            onPress={handleRefresh}
          >
            <Text style={styles.refreshButtonText}>Refresh</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.dateText}>{getCurrentDate()}</Text>
        <View style={styles.titleRow}>
          <Text style={styles.titleText}>Today</Text>
        </View>
        <TouchableOpacity style={styles.logoutButton} onPress={handleLogout}>
          <Text style={styles.logoutButtonText}>Logout</Text>
        </TouchableOpacity>
      </View>

      {/* Main Content Card */}
      <View style={styles.contentCard}>
        {/* Main Image */}
        {content.mainImage && (
          <TouchableOpacity
            onPress={() => {
              // console.log('first');
              navigation.navigate('Home');
            }}
          >
            <Image
              source={{ uri: content.mainImage }}
              style={styles.mainImage}
              resizeMode="cover"
            />
          </TouchableOpacity>
        )}

        {/* Content Info */}
        <View style={styles.contentInfo}>
          <View style={styles.animeInfo}>
            {content.logo && (
              <Image
                source={{ uri: content.logo }}
                style={styles.logo}
                resizeMode="cover"
              />
            )}
            <View style={styles.textInfo}>
              <Text style={styles.animeTitle}>{content.title}</Text>
              <Text style={styles.animeSubtitle}>{content.subTitle}</Text>
            </View>
            <TouchableOpacity
              style={styles.refreshButton}
              onPress={handleRefresh}
              disabled={isRefreshing}
            >
              <Text style={styles.refreshButtonText}>
                {isRefreshing ? 'REFRESHING...' : 'REFRESH'}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>

      {/* Full Content Display */}

      {/* Error Display for refresh errors */}
      {error && content && (
        <ErrorDisplay
          error={error}
          onRetry={handleRetry}
          style={styles.refreshError}
        />
      )}

      {/* Bottom spacing */}
      <View style={styles.bottomSpacing} />

      {/* Loading overlay for retries */}
      {isRetrying && (
        <View style={styles.loadingOverlay}>
          <LoadingSpinner text="Retrying..." />
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  scrollView: {
    flex: 1,
  },
  header: {
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 10,
  },
  dateText: {
    fontSize: 12,
    color: '#888',
    marginBottom: 4,
  },
  titleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  titleText: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#fff',
    marginRight: 10,
  },
  vsText: {
    fontSize: 16,
    color: '#888',
  },
  logoutButton: {
    position: 'absolute',
    top: 20,
    right: 20,
    backgroundColor: '#ff4444',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
  },
  logoutButtonText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '600',
  },
  contentCard: {
    backgroundColor: '#fff',
    marginHorizontal: 20,
    borderRadius: 16,
    overflow: 'hidden',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    marginBottom: 20,
  },
  mainImage: {
    width: '100%',
    height: 380,
  },
  contentInfo: {
    padding: 16,
  },
  animeInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  logo: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 12,
  },
  textInfo: {
    flex: 1,
  },
  animeTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 2,
  },
  animeSubtitle: {
    fontSize: 12,
    color: '#666',
    maxWidth: 150,
  },
  refreshButton: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 6,
  },
  refreshButtonText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '600',
  },
  fullContentContainer: {
    backgroundColor: '#fff',
    marginHorizontal: 20,
    borderRadius: 16,
    padding: 20,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
  },
  contentTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
    textAlign: 'center',
  },
  refreshIndicator: {
    fontSize: 14,
    color: '#007AFF',
    textAlign: 'center',
    marginBottom: 16,
    fontStyle: 'italic',
  },
  imageContainer: {
    marginBottom: 16,
  },
  thumbnailImage: {
    width: '100%',
    height: 250,
    borderRadius: 12,
  },
  htmlContentContainer: {
    marginBottom: 20,
  },
  paragraph: {
    fontSize: 16,
    lineHeight: 26,
    color: '#333',
    marginBottom: 16,
    textAlign: 'justify',
  },
  heading1: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 16,
    marginTop: 8,
  },
  heading2: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 12,
    marginTop: 8,
  },
  heading3: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
    marginTop: 8,
  },
  bodyText: {
    fontSize: 16,
    lineHeight: 26,
    color: '#333',
  },
  userInfo: {
    borderTopWidth: 1,
    borderTopColor: '#eee',
    paddingTop: 16,
  },
  userName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  contentId: {
    fontSize: 12,
    color: '#888',
  },
  fullScreenError: {
    margin: 16,
  },
  refreshError: {
    margin: 16,
  },
  noContentContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  noContentText: {
    fontSize: 18,
    color: '#666',
    textAlign: 'center',
    marginBottom: 20,
  },
  bottomSpacing: {
    height: 40,
  },
  loadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default ContentScreen;
