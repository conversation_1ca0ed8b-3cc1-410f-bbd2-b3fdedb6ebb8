import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  KeyboardAvoidingView,
  Platform,
  Alert,
} from 'react-native';
import { StackNavigationProp } from '@react-navigation/stack';
import LoadingSpinner from '../components/LoadingSpinner';
import ErrorDisplay from '../components/ErrorDisplay';
import { useErrorHandler } from '../hooks/useErrorHandler';
import ApiService from '../services/ApiService';
import AppStorage from '../storage/AsyncStorage';
import { RootStackParamList } from '../navigation/AppNavigator';

type LoginScreenNavigationProp = StackNavigationProp<
  RootStackParamList,
  'Login'
>;

interface LoginScreenProps {
  navigation: LoginScreenNavigationProp;
}

const LoginScreen: React.FC<LoginScreenProps> = ({ navigation }) => {
  const [email, setEmail] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isCheckingAuth, setIsCheckingAuth] = useState(true);
  const { error, setError, clearError, retry, isRetrying } = useErrorHandler();

  useEffect(() => {
    // Check if user is already logged in
    checkExistingAuth();
  }, []);

  const checkExistingAuth = async () => {
    try {
      const isLoggedIn = await AppStorage.isLoggedIn();
      const token = await AppStorage.getAuthToken();
      const userEmail = await AppStorage.getUserEmail();

      if (isLoggedIn && token && userEmail) {
        // Set token in API service
        ApiService.setToken(token);
        // Navigate to home screen
        navigation.replace('Home');
      } else {
        // Pre-fill email if available
        if (userEmail) {
          setEmail(userEmail);
        }
      }
    } catch (error) {
      console.error('Error checking existing auth:', error);
    } finally {
      setIsCheckingAuth(false);
    }
  };

  const handleLogin = async () => {
    if (!email.trim()) {
      Alert.alert('Error', 'Please enter your email address');
      return;
    }

    clearError();
    setIsLoading(true);

    try {
      const token = await ApiService.generateToken(email.trim());

      await AppStorage.setAuthData(token, email.trim());

      // Navigate to content screen
      navigation.replace('Home');
    } catch (err) {
      setError(err, 'login');
    } finally {
      setIsLoading(false);
    }
  };

  const handleRetry = () => {
    retry(async () => {
      const token = await ApiService.generateToken(email.trim());
      await AppStorage.setAuthData(token, email.trim());
      navigation.replace('Home');
    });
  };

  if (isCheckingAuth) {
    return <LoadingSpinner text="Checking authentication..." />;
  }

  if (isLoading || isRetrying) {
    return (
      <LoadingSpinner
        text={isRetrying ? 'Retrying...' : 'Authenticating...'}
        style={styles.container}
      />
    );
  }

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <View style={styles.content}>
        <Text style={styles.title}>Welcome</Text>
        <Text style={styles.subtitle}>Enter your email to continue</Text>

        <View style={styles.inputContainer}>
          <TextInput
            style={styles.input}
            placeholder="Enter your email"
            value={email}
            onChangeText={setEmail}
            keyboardType="email-address"
            autoCapitalize="none"
            autoCorrect={false}
            editable={!isLoading}
          />
        </View>

        {error && (
          <ErrorDisplay
            error={error}
            onRetry={handleRetry}
            style={styles.errorContainer}
          />
        )}

        <TouchableOpacity
          style={[
            styles.loginButton,
            (!email.trim() || isLoading) && styles.disabledButton,
          ]}
          onPress={handleLogin}
          disabled={!email.trim() || isLoading}
        >
          <Text style={styles.loginButtonText}>
            {isLoading ? 'Authenticating...' : 'Login'}
          </Text>
        </TouchableOpacity>

        <Text style={styles.infoText}>
          We'll use your email to generate a secure authentication token
        </Text>
      </View>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    paddingHorizontal: 24,
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#333',
    textAlign: 'center',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 48,
  },
  inputContainer: {
    marginBottom: 24,
  },
  input: {
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 16,
    fontSize: 16,
    color: '#333',
  },
  errorContainer: {
    marginBottom: 24,
  },
  loginButton: {
    backgroundColor: '#007AFF',
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
    marginBottom: 24,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  disabledButton: {
    backgroundColor: '#ccc',
    elevation: 0,
    shadowOpacity: 0,
  },
  loginButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  infoText: {
    fontSize: 14,
    color: '#888',
    textAlign: 'center',
    lineHeight: 20,
  },
});

export default LoginScreen;
